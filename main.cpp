#include <algorithm>
#include <vector>
#include <string>
#include <iostream>
#include <execution> // For parallel sort (C++17)
#include <cmath>    // For std::abs, std::sqrt
#include <limits>   // For std::numeric_limits

// Transaction struct
struct Transaction {
    double amount;
    std::string id;

    bool operator<(const Transaction& other) const {
        if (amount != other.amount) return amount < other.amount;
        return id < other.id;
    }

    bool operator==(const Transaction& other) const {
        return std::abs(amount - other.amount) < 1e-9 && id == other.id;
    }
};

// Configurable validator for complex rules
class TransactionValidator {
private:
    double min_amount;
    double max_amount;
    std::vector<std::pair<double, double>> forbidden_ranges;
    bool check_outliers;
    double outlier_threshold; // e.g., 3 standard deviations
    std::string id_pattern;  // Simplified: reject IDs starting with "INVALID"

public:
    TransactionValidator(
        double min = 0.0, double max = 10000.0,
        const std::vector<std::pair<double, double>>& ranges = {},
        bool outliers = false, double threshold = 3.0,
        const std::string& pattern = "INVALID"
    ) : min_amount(min), max_amount(max), forbidden_ranges(ranges),
        check_outliers(outliers), outlier_threshold(threshold), id_pattern(pattern) {}

    // Compute mean and stddev for outlier detection
    static std::pair<double, double> compute_stats(const std::vector<Transaction>& transactions) {
        double sum = 0.0, sum_sq = 0.0;
        size_t count = 0;
        for (const auto& t : transactions) {
            if (t.amount > 0.0) { // Basic validity for stats
                sum += t.amount;
                sum_sq += t.amount * t.amount;
                ++count;
            }
        }
        if (count == 0) return {0.0, 0.0};
        double mean = sum / count;
        double variance = (sum_sq / count) - (mean * mean);
        return {mean, std::sqrt(variance)};
    }

    bool operator()(const Transaction& t, const std::vector<Transaction>& transactions = {}) const {
        // Basic range check
        if (t.amount <= min_amount || t.amount > max_amount) return true;

        // Forbidden ranges
        for (const auto& [low, high] : forbidden_ranges) {
            if (t.amount >= low && t.amount <= high) return true;
        }

        // Outlier detection (if enabled)
        if (check_outliers && !transactions.empty()) {
            auto [mean, stddev] = compute_stats(transactions);
            if (stddev > 0 && std::abs(t.amount - mean) > outlier_threshold * stddev) {
                return true;
            }
        }

        // ID pattern check
        if (t.id.find(id_pattern) == 0) return true;

        return false;
    }
};

void process_transactions(std::vector<Transaction>& transactions) {
    // Configure validator with complex rules
    TransactionValidator is_wrong(
        0.0, 10000.0, // Min/max amount
        {{1000.0, 2000.0}}, // Forbidden range example
        true, 3.0, // Outlier detection: 3 stddev
        "INVALID" // Reject IDs starting with "INVALID"
    );

    // Step 1: Remove invalid transactions using iterators
    auto valid_end = std::remove_if(
        transactions.begin(), transactions.end(),
        [&is_wrong, &transactions](const Transaction& t) {
            return is_wrong(t, transactions); // Pass transactions for outlier stats
        }
    );
    transactions.erase(valid_end, transactions.end());

    // Step 2: Sort by amount (and id as tiebreaker)
    std::sort(transactions.begin(), transactions.end());

    // Step 3: Remove duplicates
    auto unique_end = std::unique(transactions.begin(), transactions.end());
    transactions.erase(unique_end, transactions.end());
}

int main() {
    // Example input
    std::vector<Transaction> transactions = {
        {100.0, "T1"},
        {-50.0, "T2"},
        {200.0, "T3"},
        {100.0, "T1"}, // Duplicate
        {300.0, "T4"},
        {0.0, "T5"},
        {400.0, "T6"},
        {200.0, "T3"}, // Duplicate
        {5000.0, "T7"},
        {15000.0, "T8"},
        {1500.0, "INVALID1"}, // Invalid ID
        {9999.9, "T9"}, // Outlier (if significantly far from mean)
    };

    // Print original
    std::cout << "Original:\n";
    for (const auto& t : transactions) {
        std::cout << "Amount: " << t.amount << ", ID: " << t.id << "\n";
    }

    // Process transactions
    process_transactions(transactions);

    // Print result
    std::cout << "\nValid unique sorted:\n";
    for (const auto& t : transactions) {
        std::cout << "Amount: " << t.amount << ", ID: " << t.id << "\n";
    }

    return 0;
}